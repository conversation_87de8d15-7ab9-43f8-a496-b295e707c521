from fpdf import FPDF
from datetime import datetime

class PDF(FPDF):
    def header(self):
        self.set_font("Helvetica", "B", 14)
        self.cell(0, 10, "Apple Inc. - Real-Time Company Report", ln=True, align="C")
        self.ln(2)

    def chapter_title(self, title):
        self.set_font("Helvetica", "B", 12)
        self.set_fill_color(230, 230, 230)
        self.cell(0, 8, title, ln=True, fill=True)
        self.ln(2)

    def chapter_body(self, body):
        self.set_font("Helvetica", "", 10)
        self.multi_cell(0, 6, body)
        self.ln()

pdf = PDF()
pdf.add_page()

# 1. Company Information
pdf.chapter_title("COMPANY INFORMATION")
pdf.set_font("Helvetica", "", 10)
company_info = [
    ["Company:", "Apple Inc."],
    ["Ticker:", "AAPL"],
    ["Sector:", "Technology"],
    ["Industry:", "Consumer Electronics"],
    ["Region:", "AMER"],
    ["Country:", "USA"],
    ["As of Date:", datetime.now().strftime("%m/%d/%Y")],
    ["Short-Term Risk Score:", "0.3"],
    ["Long-Term Risk Score:", "1.2"],
    ["Credit Rating:", "AA+"],
]
for label, value in company_info:
    pdf.cell(50, 6, label, border=0)
    pdf.cell(0, 6, value, ln=True)

# Heatmap (Simple representation)
pdf.ln(4)
pdf.cell(0, 6, "Risk HeatMap: 🟩🟩🟨🟧🟥", ln=True)

# 2. Business Description
pdf.chapter_title("BUSINESS DESCRIPTION")
pdf.chapter_body(
    "Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, "
    "and accessories. The company sells a range of services, including cloud storage, digital content, "
    "and payment solutions. Apple serves global markets through its retail stores, online platforms, "
    "and third-party distributors."
)

# 3. Key Events Timeline
pdf.chapter_title("KEY EVENTS TIMELINE (Past 12 Months)")
events = [
    ("10/27/2023", "Apple launches new M3-powered MacBook Pro and iMac models", "Positive"),
    ("11/02/2023", "Q4 FY2023 results beat estimates but iPhone sales slightly miss", "Neutral"),
    ("01/17/2024", "Apple pauses AR headset development amid cost concerns", "Negative"),
    ("04/25/2024", "Apple announces major AI features for iOS 18", "Positive"),
    ("07/28/2024", "Apple faces EU antitrust fine over App Store practices", "Negative"),
]
for date, desc, sentiment in events:
    sentiment_icon = {"Positive": "🟢", "Negative": "🔴", "Neutral": "🟡"}[sentiment]
    pdf.multi_cell(0, 6, f"{date} - {desc} {sentiment_icon}")
    pdf.ln(0.5)

# 4. Summary Points
pdf.chapter_title("SUMMARY POINTS")

pdf.set_font("Helvetica", "B", 10)
pdf.cell(0, 6, "Company Level:", ln=True)
pdf.set_font("Helvetica", "", 10)
pdf.multi_cell(0, 6, 
    "Apple's diversification into AI and new Mac hardware has boosted investor confidence. "
    "However, regulatory pressures in the EU and slowing iPhone sales remain challenges."
)

pdf.ln(2)
pdf.set_font("Helvetica", "B", 10)
pdf.cell(0, 6, "Industry Level:", ln=True)
pdf.set_font("Helvetica", "", 10)
pdf.multi_cell(0, 6, 
    "The consumer electronics industry is seeing strong demand for AI-integrated devices, "
    "but supply chain constraints and competition from Asian OEMs are intensifying."
)

pdf.ln(2)
pdf.set_font("Helvetica", "B", 10)
pdf.cell(0, 6, "Macro-Economic Level:", ln=True)
pdf.set_font("Helvetica", "", 10)
pdf.multi_cell(0, 6, 
    "Global inflation is easing, but interest rates remain high in the U.S., impacting consumer spending. "
    "Employment levels are stable, supporting steady demand."
)

# 5. Impact Indicators
pdf.chapter_title("IMPACT INDICATORS")
impacts = [
    ("Company", "↑"),
    ("Industry", "→"),
    ("Macro-Economic", "↑"),
]
for area, arrow in impacts:
    pdf.cell(50, 6, area, border=0)
    pdf.cell(0, 6, arrow, ln=True)

# Save PDF
pdf.output("apple_company_report.pdf")
print("✅ Report generated: apple_company_report.pdf")
